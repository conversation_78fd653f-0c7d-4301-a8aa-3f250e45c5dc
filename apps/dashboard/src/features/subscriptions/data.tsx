import { Bar<PERSON>ata<PERSON><PERSON> } from "@mass/shared/components/charts/bar";
import { Table<PERSON>hart } from "@mass/shared/components/charts/table";
import { getDateFunctionsForGranularity } from "@mass/shared/components/charts/utils";
import { Button } from "@mass/shared/components/ui/button";
import { Label } from "@mass/shared/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@mass/shared/components/ui/select";
import { Separator } from "@mass/shared/components/ui/separator";
import { useDateLocale } from "@mass/shared/hooks/use-date-locale";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSubscriptionUsageData } from "./data/queries";
import { DatePicker } from "@mass/shared/components/ui/date-picker";
import { Switch } from "@mass/shared/components/ui/switch";
import { useModal } from "@mass/shared/components/organisms/modal/provider";
import Export from "./components/modals/export";
import { useGlobalSettings } from "@mass/shared/hooks/use-global-settings";
import api from "@/services/api";
import { Checkbox } from "@mass/shared/components/ui/checkbox";

type Granularity = "day" | "month" | "year";
type ChartType = "bar" | "table";
export const   DataTab = (props: { subscriptionId: string }) => {
  const { t } = useTranslation("subscriptions");
  const [chosenDate, setChosenDate] = useState<Date | undefined>(undefined);
  const [chosenEndDate, setChosenEndDate] = useState<Date | undefined>(undefined);
  const [granularity, setGranularity] = useState<Granularity>("month");
  const [compareTo, setCompareTo] = useState<string[]>([]);
  const [chartType, setChartType] = useState<ChartType>("bar");
  const [error, setError] = useState<string | null>(null);
  const [threeTime, setThreeTime] = useState(false);
  const dateLocale = useDateLocale();

  const [isSelectingDateRange, setIsSelectingDateRange] = useState(false);

  const { data: _dateRangeLimits } = useGlobalSettings(
    "subscriptions.usage.date-limits",
    api
  ) as any; // todo: any
  const dateRangeLimits = Object.fromEntries(
    Object.entries(_dateRangeLimits?.value ?? {}).map(([k, v]) => [
      k,
      parseInt(v as string),
    ] as const)
  );
  console.log("dateRangeLimits", dateRangeLimits);

  const [queryDrawerOpen, setQueryDrawerOpen] = useState(false);

  const querySettings = {
    day: {
      previousTimeframe: dateRangeLimits?.dayPrev ?? 10,
      // todo: leap years
      totalTimeframe: dateRangeLimits?.dayMax ?? (2 * 365),
      compare: ["average"],
      queryGranularity: "day",
    },
    month: {
      previousTimeframe: dateRangeLimits?.monthPrev ?? 6,
      totalTimeframe: dateRangeLimits?.monthMax ?? 24,
      compare: null,
      queryGranularity: "month",
    },
    year: {
      previousTimeframe: null,
      totalTimeframe: dateRangeLimits?.yearMax ?? 2,
      compare: null,
      queryGranularity: "month",
    },
  } as const;

  // todo
  const isThreeTime = (data: any) =>
    data.T1 !== null && data.T2 !== null && data.T3 !== null;

  const currentQuerySetting = querySettings[granularity];

  const dateFunctions = getDateFunctionsForGranularity(granularity, dateLocale);

  const isDoingPastQuery =
    compareTo.includes("query") && currentQuerySetting.previousTimeframe;

  const startDate = chosenDate
    ? isDoingPastQuery
      ? dateFunctions.add(
          dateFunctions.start(chosenDate),
          -currentQuerySetting.previousTimeframe
        )
      : dateFunctions.start(chosenDate)
    : undefined;

  const endDate = isSelectingDateRange ? dateFunctions.start(dateFunctions.add(chosenEndDate!, 1)) : chosenDate
    ? dateFunctions.start(dateFunctions.add(chosenDate, 1))
    : undefined;

  const { data, mutate, reset } = useSubscriptionUsageData(
    props.subscriptionId
  );

  const { open } = useModal();

  // todo: any
  const enableThreeTime = (data?.usageData ?? []).some((d: any) =>
    isThreeTime(d.data)
  );

  const limits = {
    day: 365 * 2,
    month: 24,
    year: 2,
  };

  const rangePickerOptions = [...Array(limits[granularity]).keys()]
    .map((index) => {
      let date = new Date();
      const { start, add, label } = getDateFunctionsForGranularity(
        granularity,
        dateLocale
      );

      date = start(date);
      date = add(date, -index);

      const value = date.toISOString();
      return {
        date,
        component: (
          <SelectItem key={value} value={value}>
            {label(date)}
          </SelectItem>
        ),
      };
    })
    .map((item) => item.component);

  const rangeEndPickerOptions = [...Array(limits[granularity]).keys()]
    .slice(Math.max(0, rangePickerOptions.findIndex(
        item => item.props.value === chosenDate?.toISOString()
      ) - (querySettings[granularity].previousTimeframe ?? 0) + 1
    ), rangePickerOptions.findIndex(
        item => item.props.value === chosenDate?.toISOString()
      )).map((index) => {
      let date = new Date();
      const { start, add, label } = getDateFunctionsForGranularity(
        granularity,
        dateLocale
      );

      date = start(date);
      date = add(date, -index);

      const value = date.toISOString();

      return <SelectItem key={`rev-${value}`} value={value}>
            {label(date)}
          </SelectItem>
    })

  useEffect(() => {
    if(rangePickerOptions.at(0)?.props.value) {
      setChosenDate(new Date(rangePickerOptions.at(0)?.props.value));
    }
  }, []);

  const resetState = () => {
    reset();
    setQueryDrawerOpen(false);
    setChosenDate(undefined);
    setChosenEndDate(undefined);
    setGranularity("month");
    setCompareTo([]);
    setError(null);
  };

  const doQuery = () => {
    if (!chosenDate || chosenDate >= new Date())
      return setError(t("usage.query_invalid_date_error"));

    if(isSelectingDateRange && (!chosenEndDate || chosenEndDate < chosenDate)) {
      return setError(t("usage.query_end_date_prev_error"));
    }
    setError(null);
    mutate({
      startDate,
      endDate,
      granularity: currentQuerySetting.queryGranularity,
      compareTo: [...compareTo.filter((compare) => compare !== "query"), 
        "average",
        "similar-consumer-city",
        "similar-consumer-district"],
    });
  };

  return data ? (
    <>
      <div className="flex flex-col gap-2 w-full h-full">
        <div className="flex flex-col md:flex-row md:justify-between items-center mb-4 w-full">
          <div className="text-lg mb-4 md:mb-0 w-full md:w-auto text-center md:text-left">{t("usage.data_title")}</div>
          <div className="flex flex-col md:flex-row w-full md:w-auto md:gap-4">
            {enableThreeTime ? (
              <Select
                value={threeTime ? "1" : "0"}
                onValueChange={(value) => setThreeTime(value === "1")}
              >
                <SelectTrigger className="mb-2 md:mb-0 w-full">
                  <SelectValue
                    placeholder={t("usage.three_time_placeholder")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">{t("usage.single_time")}</SelectItem>
                  <SelectItem value="1">{t("usage.three_time")}</SelectItem>
                </SelectContent>
              </Select>
            ) : null}
            <Select
              value={chartType}
              onValueChange={(value) => setChartType(value as ChartType)}
            >
              <SelectTrigger className="mb-2 md:mb-0 w-full">
                <SelectValue placeholder={t("usage.range_placeholder")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">{t("usage.chart_type_bar")}</SelectItem>
                <SelectItem value="table">
                  {t("usage.chart_type_table")}
                </SelectItem>
              </SelectContent>
            </Select>
            <div className="flex justify-between gap-2 md:block">
              <Button onClick={resetState} variant="outline" className="flex-1 md:flex-none">
                {t("usage.query_clear_button")}
              </Button>
              <Button
                onClick={() => {
                  open(Export, {
                    title: t("usage.export.title"),
                    subscriptionId: props.subscriptionId,
                    startDate,
                    endDate,
                    granularity: currentQuerySetting.queryGranularity,
                    originalGranularity: granularity,
                    compareTo: [...compareTo.filter((compare) => compare !== "query"), 
                      "average",
                      "similar-consumer-city",
                      "similar-consumer-district"
                    ],
                  });
                }}
                className="flex-1 md:flex-none"
              >
                {t("usage.query_export_button")}
              </Button>
            </div>
          </div>
        </div>
        <div className="mt-4 w-full flex-1 flex items-start max-h-[55vh]">
          {React.createElement(
            { bar: BarDataChart, table: TableChart }[chartType],
            {
              granularity: data.requestedGranularity,
              originalGranularity: granularity,
              compareTo: data.requestedCompareTo,
              startDate: data.requestedStartDate,
              endDate: data.requestedEndDate,
              data: data.usageData,
              unit: data.unit,
              compare: data.compare,
              threeTime,
              className: "w-full",
              responsiveMode: "responsive",
            }
          )}
        </div>
        <p className={`text-gray-600 text-sm ${chartType === "table" ? "mt-20" : "mt-10"}`}>
          *{t("chartNote")}
        </p>
      </div>
    </>
  ) : (
    <>
      <div className="flex flex-col md:flex-row md:justify-between items-center mb-4 w-full">
        <div className="text-lg mb-4 md:mb-0 w-full md:w-auto text-center md:text-left">{t("usage.query_title")}</div>
        <div className="flex flex-col md:flex-row w-full md:w-auto gap-4">
          <Button onClick={resetState} variant="outline" className="md:mb-0 w-full md:w-auto order-2">
            {t("usage.query_clear_button")}
          </Button>
          <Button onClick={doQuery} className="w-full md:w-auto order-1 md:order-last">
            {t("usage.query_apply_button")}
          </Button>
        </div>
      </div>
      <Separator />
      <div className="w-full py-4 flex flex-col md:flex-row gap-4 md:gap-0 md:items-start">
        <div className="font-normal text-sm flex-1">
          <div className="font-medium">{t("usage.daterange_title")}</div>
          <div className="">{t("usage.daterange_desc")}</div>
        </div>
        <div className="flex gap-5 flex-1 flex-col text-sm">
          <div className="flex flex-col gap-2">
            <Label>{t("usage.daterange_label")}</Label>
            <Select
              value={granularity}
              onValueChange={(value) => {
                if (value !== granularity) {
                  setChosenDate(undefined);
                  setCompareTo([]);
                  setChosenEndDate(undefined);
                  setIsSelectingDateRange(false);
                  setError(null);
                }
                setGranularity(value as "day" | "month" | "year");
              }}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("usage.range_placeholder")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">{t("usage.range_day")}</SelectItem>
                <SelectItem value="month">{t("usage.range_month")}</SelectItem>
                <SelectItem value="year">{t("usage.range_year")}</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2">
            <div className="flex gap-4">
              <div className="flex flex-1 flex-col gap-2">
                <Label>{t("usage.pick_date")}</Label>
                {(!isSelectingDateRange && granularity === "year") && (
                  <Select
                      defaultValue={rangePickerOptions.at(0)?.props.value}
                      value={chosenDate?.toISOString()}
                      onValueChange={(value) => setChosenDate(new Date(value))}
                      key={granularity}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue
                          placeholder={t("usage.range_month_placeholder")}
                        />
                      </SelectTrigger>
                      <SelectContent>{rangePickerOptions}</SelectContent>
                    </Select>
                )}

                {granularity === "month" && (
                  isSelectingDateRange ? <div className="flex gap-4">
                    <Select
                      defaultValue={rangePickerOptions.at(0)?.props.value}
                      value={chosenDate?.toISOString()}
                      onValueChange={(value) => {
                        setChosenDate(new Date(value))
                        setChosenEndDate(undefined)
                      }}
                      key={granularity}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue
                          placeholder={t("usage.range_month_placeholder")}
                        />
                      </SelectTrigger>
                      <SelectContent>{rangePickerOptions}</SelectContent>
                    </Select>
                    <Select
                      value={chosenEndDate?.toISOString()}
                      onValueChange={(value) => setChosenEndDate(new Date(value))}
                      key={`${granularity}-rev`}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue
                          placeholder={t("usage.range_end_month_placeholder")}
                        />
                      </SelectTrigger>
                      <SelectContent>{rangeEndPickerOptions}</SelectContent>
                    </Select>
                  </div> : 
                    <Select
                      defaultValue={rangePickerOptions.at(0)?.props.value}
                      value={chosenDate?.toISOString()}
                      onValueChange={(value) => setChosenDate(new Date(value))}
                      key={granularity}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue
                          placeholder={t("usage.range_month_placeholder")}
                        />
                      </SelectTrigger>
                      <SelectContent>{rangePickerOptions}</SelectContent>
                    </Select>
                )}

              </div>
            </div>
            {granularity !== "year" && (
              <div className="mt-4 flex gap-4 items-center">
              <Checkbox
                  checked={
                    isSelectingDateRange
                  }
                  onCheckedChange={(value) => {
                    setIsSelectingDateRange(value ? true : false)
                    setCompareTo([]);
                  }}
                  aria-label={t("select-date-range")}
                  className="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                />

                <div className="font-medium">
                  { t("select-date-range") }
                </div>
            </div>
            )}
            <div className="mt-4 flex gap-4 flex-col">
              {currentQuerySetting.previousTimeframe ? (
                  <div className="flex items-center justify-start gap-4">
                    <Switch
                      checked={compareTo.includes("query")}
                      onCheckedChange={(value) => {
                        if (value) {
                          setCompareTo(["query"]);
                        } else {
                          setCompareTo((prev) =>
                            prev.filter((item) => item !== "query")
                          );
                        }
                      }}
                      disabled={
                        (compareTo.length > 0 && !compareTo.includes("query")) || isSelectingDateRange
                      }
                    />
                    <Label>
                      {t("usage.compare.query." + granularity, {
                        range: currentQuerySetting.previousTimeframe,
                      })}
                    </Label>
                  </div>
                ) : null}
                {(currentQuerySetting.compare ?? []).map((com) => (
                  <div className="flex items-center justify-start gap-4" key={com}>
                    <Switch
                      checked={compareTo.includes(com)}
                      onCheckedChange={(value) => {
                        if (value) {
                          setCompareTo((prev) => [
                            ...prev.filter((item) => item !== "query"),
                            com,
                          ]);
                        } else {
                          setCompareTo((prev) =>
                            prev.filter((item) => item !== com)
                          );
                        }
                      }}
                      disabled={compareTo.includes("query") || isSelectingDateRange}
                    />
                    <Label>{t("usage.compare." + com)}</Label>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>
      {!currentQuerySetting.previousTimeframe &&
      (currentQuerySetting.compare ?? []).length === 0 ? null : (
        <>
          {error && (
            <div className="flex justify-center items-center text-sm">
              <div className="text-red-500">{error}</div>
            </div>
          )}
        </>
      )}
    </>
  );
};
