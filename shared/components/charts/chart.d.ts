export interface ChartData {
  T0: number;
  T1: number | null;
  T2: number | null;
  T3: number | null;
}

export interface ChartDataPoint {
  timeframe: Date;
  data: ChartData;
}

export interface ChartCompare {
  average: ChartData | null;
  cityEntry: ChartData | null;
  districtEntry: ChartData | null;
}

export interface ChartProps {
  data: ChartDataPoint[];
  compare: ChartCompare | null;
  granularity: string;
  originalGranularity: string;
  compareTo: string | null;
  startDate: Date;
  endDate: Date;
  unit: string;
  threeTime: boolean;
}
